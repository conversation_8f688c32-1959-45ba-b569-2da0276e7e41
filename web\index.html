<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="sachdientudemo">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>Sách Đ<PERSON>ện T<PERSON> H<PERSON></title>
  <link rel="manifest" href="manifest.json">

  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js" defer></script>
  
  <!-- Model Viewer for 3D GLB files -->
  <script type="module" src="https://unpkg.com/@google/model-viewer@^3.4.0/dist/model-viewer.min.js"></script>
  <script nomodule src="https://unpkg.com/@google/model-viewer@^3.4.0/dist/model-viewer-legacy.js"></script>
  
  <!-- Three.js Library -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/controls/OrbitControls.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/loaders/GLTFLoader.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/loaders/DRACOLoader.js"></script>
  
  <!-- Babylon.js Library -->
  <script src="https://cdn.babylonjs.com/babylon.js"></script>
  <script src="https://cdn.babylonjs.com/loaders/babylonjs.loaders.min.js"></script>
  <script src="https://cdn.babylonjs.com/materialsLibrary/babylonjs.materials.min.js"></script>
  
  <!-- Convai Web SDK -->
  <script type="module">
    import { ConvaiClient } from 'https://unpkg.com/convai-web-sdk@latest/dist/convai-web-sdk.js';
    window.ConvaiClient = ConvaiClient;
  </script>
  
  <!-- Fallback cho browsers không hỗ trợ ES modules -->
  <script nomodule src="https://convai-web-sdk.s3.amazonaws.com/convai-web-sdk.js"></script>
  
  <!-- Custom WebRenderer Scripts -->
  <script src="js/three-renderer.js"></script>
  <script src="js/babylon-renderer.js"></script>

  <!-- Unity WebGL Support -->
  <script>
    // Unity WebGL configuration
    window.unityConfig = {
      dataUrl: "assets/Build/WebGL/Build/WebGL.data",
      frameworkUrl: "assets/Build/WebGL/Build/WebGL.framework.js",
      codeUrl: "assets/Build/WebGL/Build/WebGL.wasm",
      streamingAssetsUrl: "assets/Build/WebGL/StreamingAssets",
      companyName: "DefaultCompany",
      productName: "WebGL Convai",
      productVersion: "0.1",
    };
  </script>
</head>
<body>
  <script>
    {{flutter_js}}
    {{flutter_build_config}}
    
    window.addEventListener('load', function(ev) {
      _flutter.loader.load({
        serviceWorker: {
          serviceWorkerVersion: "{{flutter_service_worker_version}}",
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            appRunner.runApp();
          });
        }
      });
    });
  </script>
</body>
</html>
