-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:2:5-33:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:26:5-37:19
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\39b553e7a2a3f2b81a355d40a209a08e\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\39b553e7a2a3f2b81a355d40a209a08e\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d5d652e810319eccd13a850e06ba1755\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d5d652e810319eccd13a850e06ba1755\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\41dfd85eb430aaf0d39d9335c26b13d9\transformed\jetified-firebase-auth-interop-19.0.2\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\41dfd85eb430aaf0d39d9335c26b13d9\transformed\jetified-firebase-auth-interop-19.0.2\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2865c7ce03ea9d7b1bed052870d1372a\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2865c7ce03ea9d7b1bed052870d1372a\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\174494723129fe6dc6038c0300486d54\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\174494723129fe6dc6038c0300486d54\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e8a700b76c0b3f9c13eed3092213af62\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e8a700b76c0b3f9c13eed3092213af62\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\18136314431906453e0a3b0064a34c34\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\18136314431906453e0a3b0064a34c34\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a52b51be20a14d0b8a756eca25279495\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a52b51be20a14d0b8a756eca25279495\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\94305a301053fa4cadcf5a087b7a53c5\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\94305a301053fa4cadcf5a087b7a53c5\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16b6cef8eb4091cf945b8d934d2cfd69\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16b6cef8eb4091cf945b8d934d2cfd69\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9af0f1cfd1f7f11e117c5c1164491d7f\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9af0f1cfd1f7f11e117c5c1164491d7f\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\906adeab1fc54800ccbfe1f925d79fc0\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\906adeab1fc54800ccbfe1f925d79fc0\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d00d3cc9188cf0f50b73233f930468\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d00d3cc9188cf0f50b73233f930468\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e12f5bab452e67e3b02a5c9fd9cc61b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e12f5bab452e67e3b02a5c9fd9cc61b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:1:1-45:12
MERGED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:1:1-45:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.4.10\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:webview_flutter_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\webview_flutter_android-4.8.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.17\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\permission_handler_android-12.1.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\sqflite_android-2.4.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\05840cd26434a51e94c2f67f7d2b7d17\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43b1080182415c63c7eb945c62b25926\transformed\webkit-1.14.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38bc8d20ff474b94f74f85045e4fb4e8\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccf2261fe75ca28c65d360b8c8c3b1fd\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79a15a92dceba3287d2de7717a435261\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a61f6ab104b7d9a0d5f94024d8f886bc\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ef2678f1ebb7c2f233ed01d9a85f9da\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\39b553e7a2a3f2b81a355d40a209a08e\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d5d652e810319eccd13a850e06ba1755\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\41dfd85eb430aaf0d39d9335c26b13d9\transformed\jetified-firebase-auth-interop-19.0.2\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2865c7ce03ea9d7b1bed052870d1372a\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\174494723129fe6dc6038c0300486d54\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e8a700b76c0b3f9c13eed3092213af62\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\18136314431906453e0a3b0064a34c34\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a52b51be20a14d0b8a756eca25279495\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\518e40a15c600a40e3d2c8ae49ba318d\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf4ac8c38f743e2b4665000353d17e89\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77674444d0352378709652b3e912c268\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f5fc23aa6fc9edadd09eede2ced1b37\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5afa0c7011656557a3346c609d003b9b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbf5b53a223f9a8724b16d69a5cc08fd\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\83f475d16b959f059ad9f24da34c87f2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ba3f3d59eb387ed1a0c3ad74574d0f2\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f09e0f903595aa0da6e59048557d638a\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a8ac8f99209a058c57cf60c07c3f6140\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5b98eb6b2458a16ed03eb065e382584\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea0409d04a80a1be2a9adfb086edff2b\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbed50419cdce6780c51a60af639ff57\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\279b54dca5c2c80f76eb634089e1705d\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\93e83977f0dcb20483c40e50ddeed575\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3f83a61a7f84ce41318866e9e003fb1\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee907f6692eb2c1a4c1040e6456c22ff\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\53917f4aea66f866e4264acc81528c36\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec90ce586fda296bb57a806c602ef6c9\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\d9b005758cd660ff450015bee6543845\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\94305a301053fa4cadcf5a087b7a53c5\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16b6cef8eb4091cf945b8d934d2cfd69\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9af0f1cfd1f7f11e117c5c1164491d7f\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\906adeab1fc54800ccbfe1f925d79fc0\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77905b615a0bd81ba1a19cccce8c83cf\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ee71b9e4ebddd7045fe5822ae9cd3b6\transformed\jetified-activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c118c78baf4a34f5413be949726fa93\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc0d03bc754a6990d02d42a9695e0228\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a469fed0d64cd7f181196e2a976e8ebd\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f27c1f138c9baec49bf322aa2d63567b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a8d612f1abde141a0d8a862167841471\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e7206d253b7b565bb1ba1012e54064e\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\43c25f44db5940861cccaeacc78fafad\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfee7d71d7db8773e5cea2bc8f5c8171\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3d5b82897ff1d947a2dcc146494854b8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9612f68c599a056f46592ffff422530b\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81d5775752d9feef5c6538aa321c788\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\fdd61e796efca33e72f2ace08a176a5d\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\2440fe89a64b57f9faf5fc148ff280af\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d00d3cc9188cf0f50b73233f930468\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fdecbf64fb324c950c3491ebe8f83e46\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\81968ac78a47b1d85e17c70a17add0e7\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e12f5bab452e67e3b02a5c9fd9cc61b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c23e4478c7e45c54339a51b0bc648a1\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b562cb1bc78762dd6e4621d4ad04d3b9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b81b294947de6804d2d1930691868a34\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c136514897bbe9810f096d6b9786e01c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1719f55d6b63719e5838779553d1ddfd\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\510eda741f6a7a51b1171059867bfa71\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34fbc1429c1cb64922dd7d2d6f43686c\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\538593cb68a8c78930e38c8c0fe85d1c\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\153d0c250ff3efa5a303d125af6f5801\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\275b7d0fd0e71fdf34e5ce484ab64e9a\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\3027b62917d813c5a511aefd0408b9e1\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:1:11-69
queries
ADDED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:39:5-44:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:41:13-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:41:21-70
data
ADDED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml:6:5-66
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.4.10\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.4.10\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\webview_flutter_android-4.8.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\webview_flutter_android-4.8.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:19:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.17\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.17\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\permission_handler_android-12.1.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\permission_handler_android-12.1.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\sqflite_android-2.4.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\sqflite_android-2.4.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\05840cd26434a51e94c2f67f7d2b7d17\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\05840cd26434a51e94c2f67f7d2b7d17\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43b1080182415c63c7eb945c62b25926\transformed\webkit-1.14.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43b1080182415c63c7eb945c62b25926\transformed\webkit-1.14.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38bc8d20ff474b94f74f85045e4fb4e8\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38bc8d20ff474b94f74f85045e4fb4e8\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccf2261fe75ca28c65d360b8c8c3b1fd\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccf2261fe75ca28c65d360b8c8c3b1fd\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79a15a92dceba3287d2de7717a435261\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79a15a92dceba3287d2de7717a435261\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a61f6ab104b7d9a0d5f94024d8f886bc\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a61f6ab104b7d9a0d5f94024d8f886bc\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ef2678f1ebb7c2f233ed01d9a85f9da\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ef2678f1ebb7c2f233ed01d9a85f9da\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\39b553e7a2a3f2b81a355d40a209a08e\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\39b553e7a2a3f2b81a355d40a209a08e\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d5d652e810319eccd13a850e06ba1755\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d5d652e810319eccd13a850e06ba1755\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\41dfd85eb430aaf0d39d9335c26b13d9\transformed\jetified-firebase-auth-interop-19.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\41dfd85eb430aaf0d39d9335c26b13d9\transformed\jetified-firebase-auth-interop-19.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2865c7ce03ea9d7b1bed052870d1372a\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2865c7ce03ea9d7b1bed052870d1372a\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\174494723129fe6dc6038c0300486d54\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\174494723129fe6dc6038c0300486d54\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e8a700b76c0b3f9c13eed3092213af62\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e8a700b76c0b3f9c13eed3092213af62\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\18136314431906453e0a3b0064a34c34\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\18136314431906453e0a3b0064a34c34\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a52b51be20a14d0b8a756eca25279495\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a52b51be20a14d0b8a756eca25279495\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\518e40a15c600a40e3d2c8ae49ba318d\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\518e40a15c600a40e3d2c8ae49ba318d\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf4ac8c38f743e2b4665000353d17e89\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf4ac8c38f743e2b4665000353d17e89\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77674444d0352378709652b3e912c268\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77674444d0352378709652b3e912c268\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f5fc23aa6fc9edadd09eede2ced1b37\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f5fc23aa6fc9edadd09eede2ced1b37\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5afa0c7011656557a3346c609d003b9b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5afa0c7011656557a3346c609d003b9b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbf5b53a223f9a8724b16d69a5cc08fd\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbf5b53a223f9a8724b16d69a5cc08fd\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\83f475d16b959f059ad9f24da34c87f2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\83f475d16b959f059ad9f24da34c87f2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ba3f3d59eb387ed1a0c3ad74574d0f2\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ba3f3d59eb387ed1a0c3ad74574d0f2\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f09e0f903595aa0da6e59048557d638a\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f09e0f903595aa0da6e59048557d638a\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a8ac8f99209a058c57cf60c07c3f6140\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a8ac8f99209a058c57cf60c07c3f6140\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5b98eb6b2458a16ed03eb065e382584\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5b98eb6b2458a16ed03eb065e382584\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea0409d04a80a1be2a9adfb086edff2b\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea0409d04a80a1be2a9adfb086edff2b\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbed50419cdce6780c51a60af639ff57\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbed50419cdce6780c51a60af639ff57\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\279b54dca5c2c80f76eb634089e1705d\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\279b54dca5c2c80f76eb634089e1705d\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\93e83977f0dcb20483c40e50ddeed575\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\93e83977f0dcb20483c40e50ddeed575\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3f83a61a7f84ce41318866e9e003fb1\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3f83a61a7f84ce41318866e9e003fb1\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee907f6692eb2c1a4c1040e6456c22ff\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee907f6692eb2c1a4c1040e6456c22ff\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\53917f4aea66f866e4264acc81528c36\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\53917f4aea66f866e4264acc81528c36\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec90ce586fda296bb57a806c602ef6c9\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec90ce586fda296bb57a806c602ef6c9\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\d9b005758cd660ff450015bee6543845\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\d9b005758cd660ff450015bee6543845\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\94305a301053fa4cadcf5a087b7a53c5\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\94305a301053fa4cadcf5a087b7a53c5\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16b6cef8eb4091cf945b8d934d2cfd69\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16b6cef8eb4091cf945b8d934d2cfd69\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9af0f1cfd1f7f11e117c5c1164491d7f\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9af0f1cfd1f7f11e117c5c1164491d7f\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\906adeab1fc54800ccbfe1f925d79fc0\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\906adeab1fc54800ccbfe1f925d79fc0\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77905b615a0bd81ba1a19cccce8c83cf\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\77905b615a0bd81ba1a19cccce8c83cf\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ee71b9e4ebddd7045fe5822ae9cd3b6\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ee71b9e4ebddd7045fe5822ae9cd3b6\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c118c78baf4a34f5413be949726fa93\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c118c78baf4a34f5413be949726fa93\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc0d03bc754a6990d02d42a9695e0228\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc0d03bc754a6990d02d42a9695e0228\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a469fed0d64cd7f181196e2a976e8ebd\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a469fed0d64cd7f181196e2a976e8ebd\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f27c1f138c9baec49bf322aa2d63567b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f27c1f138c9baec49bf322aa2d63567b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a8d612f1abde141a0d8a862167841471\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a8d612f1abde141a0d8a862167841471\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e7206d253b7b565bb1ba1012e54064e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e7206d253b7b565bb1ba1012e54064e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\43c25f44db5940861cccaeacc78fafad\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\43c25f44db5940861cccaeacc78fafad\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfee7d71d7db8773e5cea2bc8f5c8171\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfee7d71d7db8773e5cea2bc8f5c8171\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3d5b82897ff1d947a2dcc146494854b8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3d5b82897ff1d947a2dcc146494854b8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9612f68c599a056f46592ffff422530b\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9612f68c599a056f46592ffff422530b\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81d5775752d9feef5c6538aa321c788\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81d5775752d9feef5c6538aa321c788\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\fdd61e796efca33e72f2ace08a176a5d\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\fdd61e796efca33e72f2ace08a176a5d\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\2440fe89a64b57f9faf5fc148ff280af\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\2440fe89a64b57f9faf5fc148ff280af\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d00d3cc9188cf0f50b73233f930468\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d00d3cc9188cf0f50b73233f930468\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fdecbf64fb324c950c3491ebe8f83e46\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fdecbf64fb324c950c3491ebe8f83e46\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\81968ac78a47b1d85e17c70a17add0e7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\81968ac78a47b1d85e17c70a17add0e7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e12f5bab452e67e3b02a5c9fd9cc61b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e12f5bab452e67e3b02a5c9fd9cc61b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c23e4478c7e45c54339a51b0bc648a1\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c23e4478c7e45c54339a51b0bc648a1\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b562cb1bc78762dd6e4621d4ad04d3b9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b562cb1bc78762dd6e4621d4ad04d3b9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b81b294947de6804d2d1930691868a34\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b81b294947de6804d2d1930691868a34\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c136514897bbe9810f096d6b9786e01c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c136514897bbe9810f096d6b9786e01c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1719f55d6b63719e5838779553d1ddfd\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1719f55d6b63719e5838779553d1ddfd\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\510eda741f6a7a51b1171059867bfa71\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\510eda741f6a7a51b1171059867bfa71\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34fbc1429c1cb64922dd7d2d6f43686c\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34fbc1429c1cb64922dd7d2d6f43686c\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\538593cb68a8c78930e38c8c0fe85d1c\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\538593cb68a8c78930e38c8c0fe85d1c\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\153d0c250ff3efa5a303d125af6f5801\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\153d0c250ff3efa5a303d125af6f5801\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\275b7d0fd0e71fdf34e5ce484ab64e9a\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\275b7d0fd0e71fdf34e5ce484ab64e9a\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\3027b62917d813c5a511aefd0408b9e1\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\3027b62917d813c5a511aefd0408b9e1\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2865c7ce03ea9d7b1bed052870d1372a\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2865c7ce03ea9d7b1bed052870d1372a\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:16:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar
ADDED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\153d0c250ff3efa5a303d125af6f5801\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\153d0c250ff3efa5a303d125af6f5801\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:10:22-76
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:21:17-111
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:25:22-65
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4fdb5e4a2ed308ab1c239308cd087e89\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\174494723129fe6dc6038c0300486d54\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\174494723129fe6dc6038c0300486d54\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba84a8f8738b5ca1e6f0f13980c5a351\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:29:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2865c7ce03ea9d7b1bed052870d1372a\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2865c7ce03ea9d7b1bed052870d1372a\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2865c7ce03ea9d7b1bed052870d1372a\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d00d3cc9188cf0f50b73233f930468\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d00d3cc9188cf0f50b73233f930468\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\94305a301053fa4cadcf5a087b7a53c5\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\94305a301053fa4cadcf5a087b7a53c5\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\94305a301053fa4cadcf5a087b7a53c5\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\906adeab1fc54800ccbfe1f925d79fc0\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\906adeab1fc54800ccbfe1f925d79fc0\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\906adeab1fc54800ccbfe1f925d79fc0\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.sachdientudemo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.sachdientudemo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
