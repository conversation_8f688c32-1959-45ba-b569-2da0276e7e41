com.example.sachdientudemo.app-jetified-profileinstaller-1.4.0-0 C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\res
com.example.sachdientudemo.app-preference-1.2.1-1 C:\Users\<USER>\.gradle\caches\8.12\transforms\05840cd26434a51e94c2f67f7d2b7d17\transformed\preference-1.2.1\res
com.example.sachdientudemo.app-core-runtime-2.2.0-2 C:\Users\<USER>\.gradle\caches\8.12\transforms\0c23e4478c7e45c54339a51b0bc648a1\transformed\core-runtime-2.2.0\res
com.example.sachdientudemo.app-lifecycle-runtime-2.7.0-3 C:\Users\<USER>\.gradle\caches\8.12\transforms\279b54dca5c2c80f76eb634089e1705d\transformed\lifecycle-runtime-2.7.0\res
com.example.sachdientudemo.app-jetified-core-ktx-1.13.1-4 C:\Users\<USER>\.gradle\caches\8.12\transforms\2c118c78baf4a34f5413be949726fa93\transformed\jetified-core-ktx-1.13.1\res
com.example.sachdientudemo.app-jetified-activity-1.10.1-5 C:\Users\<USER>\.gradle\caches\8.12\transforms\2ee71b9e4ebddd7045fe5822ae9cd3b6\transformed\jetified-activity-1.10.1\res
com.example.sachdientudemo.app-appcompat-1.1.0-6 C:\Users\<USER>\.gradle\caches\8.12\transforms\2ef2678f1ebb7c2f233ed01d9a85f9da\transformed\appcompat-1.1.0\res
com.example.sachdientudemo.app-jetified-core-viewtree-1.0.0-7 C:\Users\<USER>\.gradle\caches\8.12\transforms\34fbc1429c1cb64922dd7d2d6f43686c\transformed\jetified-core-viewtree-1.0.0\res
com.example.sachdientudemo.app-recyclerview-1.0.0-8 C:\Users\<USER>\.gradle\caches\8.12\transforms\38bc8d20ff474b94f74f85045e4fb4e8\transformed\recyclerview-1.0.0\res
com.example.sachdientudemo.app-webkit-1.14.0-9 C:\Users\<USER>\.gradle\caches\8.12\transforms\43b1080182415c63c7eb945c62b25926\transformed\webkit-1.14.0\res
com.example.sachdientudemo.app-transition-1.4.1-10 C:\Users\<USER>\.gradle\caches\8.12\transforms\43c25f44db5940861cccaeacc78fafad\transformed\transition-1.4.1\res
com.example.sachdientudemo.app-jetified-core-1.0.0-11 C:\Users\<USER>\.gradle\caches\8.12\transforms\510eda741f6a7a51b1171059867bfa71\transformed\jetified-core-1.0.0\res
com.example.sachdientudemo.app-jetified-startup-runtime-1.1.1-12 C:\Users\<USER>\.gradle\caches\8.12\transforms\51d00d3cc9188cf0f50b73233f930468\transformed\jetified-startup-runtime-1.1.1\res
com.example.sachdientudemo.app-jetified-annotation-experimental-1.4.1-13 C:\Users\<USER>\.gradle\caches\8.12\transforms\538593cb68a8c78930e38c8c0fe85d1c\transformed\jetified-annotation-experimental-1.4.1\res
com.example.sachdientudemo.app-jetified-datastore-preferences-release-14 C:\Users\<USER>\.gradle\caches\8.12\transforms\53917f4aea66f866e4264acc81528c36\transformed\jetified-datastore-preferences-release\res
com.example.sachdientudemo.app-jetified-window-1.2.0-15 C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\res
com.example.sachdientudemo.app-jetified-lifecycle-livedata-core-ktx-2.7.0-16 C:\Users\<USER>\.gradle\caches\8.12\transforms\5ba3f3d59eb387ed1a0c3ad74574d0f2\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.sachdientudemo.app-jetified-play-services-base-18.1.0-17 C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\res
com.example.sachdientudemo.app-core-1.13.1-18 C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\res
com.example.sachdientudemo.app-jetified-lifecycle-process-2.7.0-19 C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\res
com.example.sachdientudemo.app-jetified-fragment-ktx-1.7.1-20 C:\Users\<USER>\.gradle\caches\8.12\transforms\77674444d0352378709652b3e912c268\transformed\jetified-fragment-ktx-1.7.1\res
com.example.sachdientudemo.app-fragment-1.7.1-21 C:\Users\<USER>\.gradle\caches\8.12\transforms\77905b615a0bd81ba1a19cccce8c83cf\transformed\fragment-1.7.1\res
com.example.sachdientudemo.app-slidingpanelayout-1.2.0-22 C:\Users\<USER>\.gradle\caches\8.12\transforms\79a15a92dceba3287d2de7717a435261\transformed\slidingpanelayout-1.2.0\res
com.example.sachdientudemo.app-jetified-activity-ktx-1.10.1-23 C:\Users\<USER>\.gradle\caches\8.12\transforms\7f5fc23aa6fc9edadd09eede2ced1b37\transformed\jetified-activity-ktx-1.10.1\res
com.example.sachdientudemo.app-lifecycle-livedata-2.7.0-24 C:\Users\<USER>\.gradle\caches\8.12\transforms\83f475d16b959f059ad9f24da34c87f2\transformed\lifecycle-livedata-2.7.0\res
com.example.sachdientudemo.app-jetified-play-services-basement-18.3.0-25 C:\Users\<USER>\.gradle\caches\8.12\transforms\906adeab1fc54800ccbfe1f925d79fc0\transformed\jetified-play-services-basement-18.3.0\res
com.example.sachdientudemo.app-jetified-savedstate-ktx-1.2.1-26 C:\Users\<USER>\.gradle\caches\8.12\transforms\93e83977f0dcb20483c40e50ddeed575\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.sachdientudemo.app-jetified-ads-adservices-1.0.0-beta05-27 C:\Users\<USER>\.gradle\caches\8.12\transforms\94305a301053fa4cadcf5a087b7a53c5\transformed\jetified-ads-adservices-1.0.0-beta05\res
com.example.sachdientudemo.app-jetified-appcompat-resources-1.1.0-28 C:\Users\<USER>\.gradle\caches\8.12\transforms\a469fed0d64cd7f181196e2a976e8ebd\transformed\jetified-appcompat-resources-1.1.0\res
com.example.sachdientudemo.app-jetified-window-java-1.2.0-29 C:\Users\<USER>\.gradle\caches\8.12\transforms\a61f6ab104b7d9a0d5f94024d8f886bc\transformed\jetified-window-java-1.2.0\res
com.example.sachdientudemo.app-jetified-lifecycle-runtime-ktx-2.7.0-30 C:\Users\<USER>\.gradle\caches\8.12\transforms\a8ac8f99209a058c57cf60c07c3f6140\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.sachdientudemo.app-coordinatorlayout-1.0.0-31 C:\Users\<USER>\.gradle\caches\8.12\transforms\a8d612f1abde141a0d8a862167841471\transformed\coordinatorlayout-1.0.0\res
com.example.sachdientudemo.app-jetified-play-services-measurement-api-21.6.1-32 C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\res
com.example.sachdientudemo.app-jetified-ads-adservices-java-1.0.0-beta05-33 C:\Users\<USER>\.gradle\caches\8.12\transforms\d9b005758cd660ff450015bee6543845\transformed\jetified-ads-adservices-java-1.0.0-beta05\res
com.example.sachdientudemo.app-jetified-firebase-common-20.4.3-34 C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\res
com.example.sachdientudemo.app-lifecycle-viewmodel-2.7.0-35 C:\Users\<USER>\.gradle\caches\8.12\transforms\dbed50419cdce6780c51a60af639ff57\transformed\lifecycle-viewmodel-2.7.0\res
com.example.sachdientudemo.app-jetified-savedstate-1.2.1-36 C:\Users\<USER>\.gradle\caches\8.12\transforms\e3f83a61a7f84ce41318866e9e003fb1\transformed\jetified-savedstate-1.2.1\res
com.example.sachdientudemo.app-lifecycle-livedata-core-2.7.0-37 C:\Users\<USER>\.gradle\caches\8.12\transforms\e5b98eb6b2458a16ed03eb065e382584\transformed\lifecycle-livedata-core-2.7.0\res
com.example.sachdientudemo.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-38 C:\Users\<USER>\.gradle\caches\8.12\transforms\ea0409d04a80a1be2a9adfb086edff2b\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.sachdientudemo.app-jetified-datastore-release-39 C:\Users\<USER>\.gradle\caches\8.12\transforms\ec90ce586fda296bb57a806c602ef6c9\transformed\jetified-datastore-release\res
com.example.sachdientudemo.app-jetified-datastore-core-release-40 C:\Users\<USER>\.gradle\caches\8.12\transforms\ee907f6692eb2c1a4c1040e6456c22ff\transformed\jetified-datastore-core-release\res
com.example.sachdientudemo.app-jetified-lifecycle-viewmodel-ktx-2.7.0-41 C:\Users\<USER>\.gradle\caches\8.12\transforms\f09e0f903595aa0da6e59048557d638a\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.sachdientudemo.app-jetified-tracing-1.2.0-42 C:\Users\<USER>\.gradle\caches\8.12\transforms\fdecbf64fb324c950c3491ebe8f83e46\transformed\jetified-tracing-1.2.0\res
com.example.sachdientudemo.app-google-services-43 C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\build\generated\res\google-services\debug
com.example.sachdientudemo.app-pngs-44 C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\build\generated\res\pngs\debug
com.example.sachdientudemo.app-resValues-45 C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\build\generated\res\resValues\debug
com.example.sachdientudemo.app-packageDebugResources-46 C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.sachdientudemo.app-packageDebugResources-47 C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.sachdientudemo.app-debug-48 C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.sachdientudemo.app-debug-49 C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\res
com.example.sachdientudemo.app-main-50 C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\res
com.example.sachdientudemo.app-debug-51 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\packaged_res\debug\packageDebugResources
com.example.sachdientudemo.app-debug-52 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\packaged_res\debug\packageDebugResources
com.example.sachdientudemo.app-debug-53 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\packaged_res\debug\packageDebugResources
com.example.sachdientudemo.app-debug-54 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\packaged_res\debug\packageDebugResources
com.example.sachdientudemo.app-debug-55 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.17\android\build\intermediates\packaged_res\debug\packageDebugResources
com.example.sachdientudemo.app-debug-56 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\permission_handler_android-12.1.0\android\build\intermediates\packaged_res\debug\packageDebugResources
com.example.sachdientudemo.app-debug-57 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.4.10\android\build\intermediates\packaged_res\debug\packageDebugResources
com.example.sachdientudemo.app-debug-58 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\sqflite_android-2.4.1\android\build\intermediates\packaged_res\debug\packageDebugResources
com.example.sachdientudemo.app-debug-59 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\webview_flutter_android-4.8.2\android\build\intermediates\packaged_res\debug\packageDebugResources
