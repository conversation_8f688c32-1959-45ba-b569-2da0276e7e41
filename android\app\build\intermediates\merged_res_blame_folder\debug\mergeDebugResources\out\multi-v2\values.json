{"logs": [{"outputFile": "com.example.sachdientudemo.app-mergeDebugResources-46:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\61d3d4fe2dd837d40b39785170a0ad2d\\transformed\\jetified-play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "86,87,88,89,90,91,92,93,398,399,400,401,402,403,404,405,407,408,409,410,411,412,413,414,415,3097,3507", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3972,4062,4142,4232,4322,4402,4483,4563,24128,24233,24414,24539,24646,24826,24949,25065,25335,25523,25628,25809,25934,26109,26257,26320,26382,173752,186960", "endLines": "86,87,88,89,90,91,92,93,398,399,400,401,402,403,404,405,407,408,409,410,411,412,413,414,415,3109,3525", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4057,4137,4227,4317,4397,4478,4558,4638,24228,24409,24534,24641,24821,24944,25060,25163,25518,25623,25804,25929,26104,26252,26315,26377,26456,174062,187372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\77905b615a0bd81ba1a19cccce8c83cf\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "309,325,354,3003,3008", "startColumns": "4,4,4,4,4", "startOffsets": "18825,19579,21097,171039,171209", "endLines": "309,325,354,3007,3011", "endColumns": "56,64,63,24,24", "endOffsets": "18877,19639,21156,171204,171353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5519fbbac70289e20ed689cd5c7a916a\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,308,2214,2220,3538,3546,3561", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,18765,142339,142534,187689,187971,188585", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,308,2219,2224,3545,3560,3576", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,18820,142529,142687,187966,188580,189234"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sachdientudemo\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1523,1527", "startColumns": "4,4", "startOffsets": "97406,97587", "endLines": "1526,1529", "endColumns": "12,12", "endOffsets": "97582,97751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\279b54dca5c2c80f76eb634089e1705d\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "350", "startColumns": "4", "startOffsets": "20890", "endColumns": "42", "endOffsets": "20928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2ef2678f1ebb7c2f233ed01d9a85f9da\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,82,83,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,224,225,229,230,231,232,233,234,235,261,262,263,264,265,266,267,268,304,305,306,307,312,320,321,326,348,355,356,357,358,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,429,434,435,436,437,438,439,447,448,452,456,460,465,471,478,482,486,491,495,499,503,507,511,515,521,525,531,535,541,545,550,554,557,561,567,571,577,581,587,590,594,598,602,606,610,611,612,613,616,619,622,625,629,630,631,632,633,636,638,640,642,647,648,652,658,662,663,665,676,677,681,687,691,692,693,697,724,728,729,733,761,931,957,1128,1154,1185,1193,1199,1213,1235,1240,1245,1255,1264,1273,1277,1284,1292,1299,1300,1309,1312,1315,1319,1323,1327,1330,1331,1336,1341,1351,1356,1363,1369,1370,1373,1377,1382,1384,1386,1389,1392,1394,1398,1401,1408,1411,1414,1418,1420,1424,1426,1428,1430,1434,1442,1450,1462,1468,1477,1480,1491,1494,1495,1500,1501,1530,1599,1669,1670,1680,1689,1841,1843,1847,1850,1853,1856,1859,1862,1865,1868,1872,1875,1878,1881,1885,1888,1892,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1918,1920,1921,1922,1923,1924,1925,1926,1927,1929,1930,1932,1933,1935,1937,1938,1940,1941,1942,1943,1944,1945,1947,1948,1949,1950,1951,1963,1965,1967,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1983,1984,1985,1986,1987,1988,1990,1994,1998,1999,2000,2001,2002,2003,2007,2008,2009,2010,2012,2014,2016,2018,2020,2021,2022,2023,2025,2027,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2043,2044,2045,2046,2048,2050,2051,2053,2054,2056,2058,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2073,2074,2075,2076,2078,2079,2080,2081,2082,2084,2086,2088,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2110,2185,2188,2191,2194,2208,2225,2267,2296,2323,2332,2394,2758,2789,2927,3051,3075,3081,3110,3131,3255,3283,3289,3433,3459,3526,3597,3697,3717,3772,3784,3810", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2566,2643,2881,2966,3048,3124,3200,3277,3355,3461,3567,3646,3726,3783,4643,4717,4792,4857,4923,4983,5044,5116,5189,5256,5324,5383,5442,5501,5560,5619,5673,5727,5780,5834,5888,5942,6197,6271,6350,6423,6497,6568,6640,6712,6785,6842,6900,6973,7047,7121,7196,7268,7341,7411,7482,7542,7603,7672,7741,7811,7885,7961,8025,8102,8178,8255,8320,8389,8466,8541,8610,8678,8755,8821,8882,8979,9044,9113,9212,9283,9342,9400,9457,9516,9580,9651,9723,9795,9867,9939,10006,10074,10142,10201,10264,10328,10418,10509,10569,10635,10702,10768,10838,10902,10955,11022,11083,11150,11263,11321,11384,11449,11514,11589,11662,11734,11783,11844,11905,11966,12028,12092,12156,12220,12285,12348,12408,12469,12535,12594,12654,12716,12787,12847,13403,13489,13739,13829,13916,14004,14086,14169,14259,15984,16036,16094,16139,16205,16269,16326,16383,18560,18617,18665,18714,18969,19339,19386,19644,20815,21161,21225,21287,21347,21668,21742,21812,21890,21944,22014,22099,22147,22193,22254,22317,22383,22447,22518,22581,22646,22710,22771,22832,22884,22957,23031,23100,23175,23249,23323,23464,27449,27810,27888,27978,28066,28162,28252,28834,28923,29170,29451,29703,29988,30381,30858,31080,31302,31578,31805,32035,32265,32495,32725,32952,33371,33597,34022,34252,34680,34899,35182,35390,35521,35748,36174,36399,36826,37047,37472,37592,37868,38169,38493,38784,39098,39235,39366,39471,39713,39880,40084,40292,40563,40675,40787,40892,41009,41223,41369,41509,41595,41943,42031,42277,42695,42944,43026,43124,43716,43816,44068,44492,44747,44841,44930,45167,47191,47433,47535,47788,49944,60476,61992,72623,74151,75908,76534,76954,78015,79280,79536,79772,80319,80813,81418,81616,82196,82760,83135,83253,83791,83948,84144,84417,84673,84843,84984,85048,85413,85780,86456,86720,87058,87411,87505,87691,87997,88259,88384,88511,88750,88961,89080,89273,89450,89905,90086,90208,90467,90580,90767,90869,90976,91105,91380,91888,92384,93261,93555,94125,94274,95006,95178,95262,95598,95690,97756,103002,108391,108453,109031,109615,117562,117675,117904,118064,118216,118387,118553,118722,118889,119052,119295,119465,119638,119809,120083,120282,120487,120817,120901,120997,121093,121191,121291,121393,121495,121597,121699,121801,121901,121997,122109,122238,122361,122492,122623,122721,122835,122929,123069,123203,123299,123411,123511,123627,123723,123835,123935,124075,124211,124375,124505,124663,124813,124954,125098,125233,125345,125495,125623,125751,125887,126019,126149,126279,126391,127289,127435,127579,127717,127783,127873,127949,128053,128143,128245,128353,128461,128561,128641,128733,128831,128941,129019,129125,129217,129321,129431,129553,129716,129873,129953,130053,130143,130253,130343,130584,130678,130784,130876,130976,131088,131202,131318,131434,131528,131642,131754,131856,131976,132098,132180,132284,132404,132530,132628,132722,132810,132922,133038,133160,133272,133447,133563,133649,133741,133853,133977,134044,134170,134238,134366,134510,134638,134707,134802,134917,135030,135129,135238,135349,135460,135561,135666,135766,135896,135987,136110,136204,136316,136402,136506,136602,136690,136808,136912,137016,137142,137230,137338,137438,137528,137638,137722,137824,137908,137962,138026,138132,138218,138328,138412,138816,141432,141550,141665,141745,142106,142692,144096,145440,146801,147189,149964,160053,161093,167906,172207,172958,173220,174067,174446,178724,179578,179807,184415,185425,187377,189777,193901,194645,196776,197116,198427", "endLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,82,83,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,224,225,229,230,231,232,233,234,235,261,262,263,264,265,266,267,268,304,305,306,307,312,320,321,326,348,355,356,357,358,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,429,434,435,436,437,438,446,447,451,455,459,464,470,477,481,485,490,494,498,502,506,510,514,520,524,530,534,540,544,549,553,556,560,566,570,576,580,586,589,593,597,601,605,609,610,611,612,615,618,621,624,628,629,630,631,632,635,637,639,641,646,647,651,657,661,662,664,675,676,680,686,690,691,692,696,723,727,728,732,760,930,956,1127,1153,1184,1192,1198,1212,1234,1239,1244,1254,1263,1272,1276,1283,1291,1298,1299,1308,1311,1314,1318,1322,1326,1329,1330,1335,1340,1350,1355,1362,1368,1369,1372,1376,1381,1383,1385,1388,1391,1393,1397,1400,1407,1410,1413,1417,1419,1423,1425,1427,1429,1433,1441,1449,1461,1467,1476,1479,1490,1493,1494,1499,1500,1505,1598,1668,1669,1679,1688,1689,1842,1846,1849,1852,1855,1858,1861,1864,1867,1871,1874,1877,1880,1884,1887,1891,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1917,1919,1920,1921,1922,1923,1924,1925,1926,1928,1929,1931,1932,1934,1936,1937,1939,1940,1941,1942,1943,1944,1946,1947,1948,1949,1950,1951,1964,1966,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1982,1983,1984,1985,1986,1987,1989,1993,1997,1998,1999,2000,2001,2002,2006,2007,2008,2009,2011,2013,2015,2017,2019,2020,2021,2022,2024,2026,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2042,2043,2044,2045,2047,2049,2050,2052,2053,2055,2057,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2072,2073,2074,2075,2077,2078,2079,2080,2081,2083,2085,2087,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2184,2187,2190,2193,2207,2213,2234,2295,2322,2331,2393,2752,2761,2816,2944,3074,3080,3086,3130,3254,3274,3288,3292,3438,3493,3537,3662,3716,3771,3783,3809,3816", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2638,2716,2961,3043,3119,3195,3272,3350,3456,3562,3641,3721,3778,3836,4712,4787,4852,4918,4978,5039,5111,5184,5251,5319,5378,5437,5496,5555,5614,5668,5722,5775,5829,5883,5937,5991,6266,6345,6418,6492,6563,6635,6707,6780,6837,6895,6968,7042,7116,7191,7263,7336,7406,7477,7537,7598,7667,7736,7806,7880,7956,8020,8097,8173,8250,8315,8384,8461,8536,8605,8673,8750,8816,8877,8974,9039,9108,9207,9278,9337,9395,9452,9511,9575,9646,9718,9790,9862,9934,10001,10069,10137,10196,10259,10323,10413,10504,10564,10630,10697,10763,10833,10897,10950,11017,11078,11145,11258,11316,11379,11444,11509,11584,11657,11729,11778,11839,11900,11961,12023,12087,12151,12215,12280,12343,12403,12464,12530,12589,12649,12711,12782,12842,12910,13484,13571,13824,13911,13999,14081,14164,14254,14345,16031,16089,16134,16200,16264,16321,16378,16432,18612,18660,18709,18760,18998,19381,19430,19685,20842,21220,21282,21342,21399,21737,21807,21885,21939,22009,22094,22142,22188,22249,22312,22378,22442,22513,22576,22641,22705,22766,22827,22879,22952,23026,23095,23170,23244,23318,23459,23529,27497,27883,27973,28061,28157,28247,28829,28918,29165,29446,29698,29983,30376,30853,31075,31297,31573,31800,32030,32260,32490,32720,32947,33366,33592,34017,34247,34675,34894,35177,35385,35516,35743,36169,36394,36821,37042,37467,37587,37863,38164,38488,38779,39093,39230,39361,39466,39708,39875,40079,40287,40558,40670,40782,40887,41004,41218,41364,41504,41590,41938,42026,42272,42690,42939,43021,43119,43711,43811,44063,44487,44742,44836,44925,45162,47186,47428,47530,47783,49939,60471,61987,72618,74146,75903,76529,76949,78010,79275,79531,79767,80314,80808,81413,81611,82191,82755,83130,83248,83786,83943,84139,84412,84668,84838,84979,85043,85408,85775,86451,86715,87053,87406,87500,87686,87992,88254,88379,88506,88745,88956,89075,89268,89445,89900,90081,90203,90462,90575,90762,90864,90971,91100,91375,91883,92379,93256,93550,94120,94269,95001,95173,95257,95593,95685,95963,102997,108386,108448,109026,109610,109701,117670,117899,118059,118211,118382,118548,118717,118884,119047,119290,119460,119633,119804,120078,120277,120482,120812,120896,120992,121088,121186,121286,121388,121490,121592,121694,121796,121896,121992,122104,122233,122356,122487,122618,122716,122830,122924,123064,123198,123294,123406,123506,123622,123718,123830,123930,124070,124206,124370,124500,124658,124808,124949,125093,125228,125340,125490,125618,125746,125882,126014,126144,126274,126386,126526,127430,127574,127712,127778,127868,127944,128048,128138,128240,128348,128456,128556,128636,128728,128826,128936,129014,129120,129212,129316,129426,129548,129711,129868,129948,130048,130138,130248,130338,130579,130673,130779,130871,130971,131083,131197,131313,131429,131523,131637,131749,131851,131971,132093,132175,132279,132399,132525,132623,132717,132805,132917,133033,133155,133267,133442,133558,133644,133736,133848,133972,134039,134165,134233,134361,134505,134633,134702,134797,134912,135025,135124,135233,135344,135455,135556,135661,135761,135891,135982,136105,136199,136311,136397,136501,136597,136685,136803,136907,137011,137137,137225,137333,137433,137523,137633,137717,137819,137903,137957,138021,138127,138213,138323,138407,138527,141427,141545,141660,141740,142101,142334,143204,145435,146796,147184,149959,159863,160183,162445,168473,172953,173215,173415,174441,178719,179325,179802,179953,184625,186503,187684,192798,194640,196771,197111,198422,198625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2ee71b9e4ebddd7045fe5822ae9cd3b6\\transformed\\jetified-activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "322,351", "startColumns": "4,4", "startOffsets": "19435,20933", "endColumns": "41,59", "endOffsets": "19472,20988"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\sachdientudemo\\android\\app\\build\\generated\\res\\google-services\\debug\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,174,256,360,469,589,692", "endColumns": "118,81,103,108,119,102,71", "endOffsets": "169,251,355,464,584,687,759"}, "to": {"startLines": "418,419,420,421,422,423,428", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "26556,26675,26757,26861,26970,27090,27377", "endColumns": "118,81,103,108,119,102,71", "endOffsets": "26670,26752,26856,26965,27085,27188,27444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\62e42733fc48d7e4200114b93a9c82fb\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,70,71,84,85,116,117,217,218,219,220,221,222,223,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,314,315,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,362,391,392,393,394,395,396,397,430,1952,1953,1957,1958,1962,2105,2106,2762,2779,2949,2982,3012,3045", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2721,2793,3841,3906,5996,6065,12915,12985,13053,13125,13195,13256,13330,14573,14634,14695,14757,14821,14883,14944,15012,15112,15172,15238,15311,15380,15437,15489,16437,16509,16585,16650,16709,16768,16828,16888,16948,17008,17068,17128,17188,17248,17308,17368,17427,17487,17547,17607,17667,17727,17787,17847,17907,17967,18027,18086,18146,18206,18265,18324,18383,18442,18501,19069,19104,19690,19745,19808,19863,19921,19979,20040,20103,20160,20211,20261,20322,20379,20445,20479,20514,21598,23617,23684,23756,23825,23894,23968,24040,27502,126531,126648,126849,126959,127160,138532,138604,160188,160792,168627,170358,171358,172040", "endLines": "29,70,71,84,85,116,117,217,218,219,220,221,222,223,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,314,315,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,362,391,392,393,394,395,396,397,430,1952,1956,1957,1961,1962,2105,2106,2767,2788,2981,3002,3044,3050", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2788,2876,3901,3967,6060,6123,12980,13048,13120,13190,13251,13325,13398,14629,14690,14752,14816,14878,14939,15007,15107,15167,15233,15306,15375,15432,15484,15546,16504,16580,16645,16704,16763,16823,16883,16943,17003,17063,17123,17183,17243,17303,17363,17422,17482,17542,17602,17662,17722,17782,17842,17902,17962,18022,18081,18141,18201,18260,18319,18378,18437,18496,18555,19099,19134,19740,19803,19858,19916,19974,20035,20098,20155,20206,20256,20317,20374,20440,20474,20509,20544,21663,23679,23751,23820,23889,23963,24035,24123,27568,126643,126844,126954,127155,127284,138599,138666,160386,161088,170353,171034,172035,172202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\38bc8d20ff474b94f74f85045e4fb4e8\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "226,227,228,236,237,238,313,3439", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "13576,13635,13683,14350,14425,14501,19003,184630", "endLines": "226,227,228,236,237,238,313,3458", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "13630,13678,13734,14420,14496,14568,19064,185420"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\906adeab1fc54800ccbfe1f925d79fc0\\transformed\\jetified-play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "359,406", "startColumns": "4,4", "startOffsets": "21404,25168", "endColumns": "67,166", "endOffsets": "21467,25330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dbed50419cdce6780c51a60af639ff57\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "353", "startColumns": "4", "startOffsets": "21047", "endColumns": "49", "endOffsets": "21092"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a469fed0d64cd7f181196e2a976e8ebd\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2235,2251,2257,3577,3593", "startColumns": "4,4,4,4,4", "startOffsets": "143209,143634,143812,189239,189650", "endLines": "2250,2256,2266,3592,3596", "endColumns": "24,24,24,24,24", "endOffsets": "143629,143807,144091,189645,189772"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a8d612f1abde141a0d8a862167841471\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2107,2817,2823", "startColumns": "4,4,4,4", "startOffsets": "164,138671,162450,162661", "endLines": "3,2109,2822,2906", "endColumns": "60,12,24,24", "endOffsets": "220,138811,162656,167172"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e3f83a61a7f84ce41318866e9e003fb1\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "352", "startColumns": "4", "startOffsets": "20993", "endColumns": "53", "endOffsets": "21042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\05840cd26434a51e94c2f67f7d2b7d17\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,118,254,255,256,257,258,259,260,317,318,319,360,361,416,417,424,425,431,432,433,1506,1690,1693,1699,1705,1708,1714,1718,1721,1728,1734,1737,1743,1748,1753,1760,1762,1768,1774,1782,1787,1794,1799,1805,1809,1816,1820,1826,1832,1835,1839,1840,2753,2768,2907,2945,3087,3275,3293,3357,3367,3377,3384,3390,3494,3663,3680", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,6128,15551,15615,15670,15738,15805,15870,15927,19182,19230,19278,21472,21535,26461,26499,27193,27237,27573,27712,27762,95968,109706,109811,110056,110394,110540,110880,111092,111255,111662,112000,112123,112462,112701,112958,113329,113389,113727,114013,114462,114754,115142,115447,115791,116036,116366,116573,116841,117114,117258,117459,117506,159868,160391,167177,168478,173420,179330,179958,181883,182165,182470,182732,182992,186508,192803,193333", "endLines": "63,118,254,255,256,257,258,259,260,317,318,319,360,361,416,417,424,427,431,432,433,1522,1692,1698,1704,1707,1713,1717,1720,1727,1733,1736,1742,1747,1752,1759,1761,1767,1773,1781,1786,1793,1798,1804,1808,1815,1819,1825,1831,1834,1838,1839,1840,2757,2778,2926,2948,3096,3282,3356,3366,3376,3383,3389,3432,3506,3679,3696", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,6192,15610,15665,15733,15800,15865,15922,15979,19225,19273,19334,21530,21593,26494,26551,27232,27372,27707,27757,27805,97401,109806,110051,110389,110535,110875,111087,111250,111657,111995,112118,112457,112696,112953,113324,113384,113722,114008,114457,114749,115137,115442,115786,116031,116361,116568,116836,117109,117253,117454,117501,117557,160048,160787,167901,168622,173747,179573,181878,182160,182465,182727,182987,184410,186955,193328,193896"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\43c25f44db5940861cccaeacc78fafad\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "310,311,316,323,324,343,344,345,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "18882,18922,19139,19477,19532,20549,20603,20655,20704,20765", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "18917,18964,19177,19527,19574,20598,20650,20699,20760,20810"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\51d00d3cc9188cf0f50b73233f930468\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "390", "startColumns": "4", "startOffsets": "23534", "endColumns": "82", "endOffsets": "23612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\34fbc1429c1cb64922dd7d2d6f43686c\\transformed\\jetified-core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "349", "startColumns": "4", "startOffsets": "20847", "endColumns": "42", "endOffsets": "20885"}}]}]}