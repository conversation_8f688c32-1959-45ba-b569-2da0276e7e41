// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBGZR13D-U6mIY5sH3Dbfcx2nbu8aFPScg',
    appId: '1:657175691442:web:32b3a1a3a85b809ed949ab',
    messagingSenderId: '657175691442',
    projectId: 'ebook-fdc02',
    authDomain: 'ebook-fdc02.firebaseapp.com',
    databaseURL: 'https://ebook-fdc02-default-rtdb.firebaseio.com',
    storageBucket: 'ebook-fdc02.firebasestorage.app',
    measurementId: 'G-W4M0B6EDJC',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA49_93r5iK5nbY6TykssefQjrR6cp1SgY',
    appId: '1:657175691442:android:43825081640b3976d949ab',
    messagingSenderId: '657175691442',
    projectId: 'ebook-fdc02',
    databaseURL: 'https://ebook-fdc02-default-rtdb.firebaseio.com',
    storageBucket: 'ebook-fdc02.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAl3EOfwQVN-TV867wPvfsoOpchlb2qG8w',
    appId: '1:657175691442:ios:40331a342f6d57b4d949ab',
    messagingSenderId: '657175691442',
    projectId: 'ebook-fdc02',
    databaseURL: 'https://ebook-fdc02-default-rtdb.firebaseio.com',
    storageBucket: 'ebook-fdc02.firebasestorage.app',
    iosBundleId: 'com.example.sachdientudemo',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAl3EOfwQVN-TV867wPvfsoOpchlb2qG8w',
    appId: '1:657175691442:ios:40331a342f6d57b4d949ab',
    messagingSenderId: '657175691442',
    projectId: 'ebook-fdc02',
    databaseURL: 'https://ebook-fdc02-default-rtdb.firebaseio.com',
    storageBucket: 'ebook-fdc02.firebasestorage.app',
    iosBundleId: 'com.example.sachdientudemo',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBGZR13D-U6mIY5sH3Dbfcx2nbu8aFPScg',
    appId: '1:657175691442:web:600f5db7fe70bb90d949ab',
    messagingSenderId: '657175691442',
    projectId: 'ebook-fdc02',
    authDomain: 'ebook-fdc02.firebaseapp.com',
    databaseURL: 'https://ebook-fdc02-default-rtdb.firebaseio.com',
    storageBucket: 'ebook-fdc02.firebasestorage.app',
    measurementId: 'G-N28KL0MH0Z',
  );
}
