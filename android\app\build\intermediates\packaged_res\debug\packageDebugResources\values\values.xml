<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="firebase_database_url" translatable="false">https://ebook-fdc02-default-rtdb.firebaseio.com</string>
    <string name="gcm_defaultSenderId" translatable="false">657175691442</string>
    <string name="google_api_key" translatable="false">AIzaSyA49_93r5iK5nbY6TykssefQjrR6cp1SgY</string>
    <string name="google_app_id" translatable="false">1:657175691442:android:43825081640b3976d949ab</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyA49_93r5iK5nbY6TykssefQjrR6cp1SgY</string>
    <string name="google_storage_bucket" translatable="false">ebook-fdc02.firebasestorage.app</string>
    <string name="project_id" translatable="false">ebook-fdc02</string>
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
</resources>