<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\res"><file name="launch_background" path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\res\drawable\launch_background.xml" qualifiers="" type="drawable"/><file name="launch_background" path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\res\drawable-v21\launch_background.xml" qualifiers="v21" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\build\generated\res\google-services\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\build\generated\res\google-services\debug"><file path="C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\build\generated\res\google-services\debug\values\values.xml" qualifiers=""><string name="firebase_database_url" translatable="false">https://ebook-fdc02-default-rtdb.firebaseio.com</string><string name="gcm_defaultSenderId" translatable="false">657175691442</string><string name="google_api_key" translatable="false">AIzaSyA49_93r5iK5nbY6TykssefQjrR6cp1SgY</string><string name="google_app_id" translatable="false">1:657175691442:android:43825081640b3976d949ab</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyA49_93r5iK5nbY6TykssefQjrR6cp1SgY</string><string name="google_storage_bucket" translatable="false">ebook-fdc02.firebasestorage.app</string><string name="project_id" translatable="false">ebook-fdc02</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="legacy_api_res$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="legacy_api_res" generated-set="legacy_api_res$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>