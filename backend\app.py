from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import os
import requests
import cloudinary
import cloudinary.uploader
import cloudinary.api
from werkzeug.utils import secure_filename
import PyPDF2
import io
import base64
import google.generativeai as genai
import json
from datetime import datetime
import uuid

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'pdf'}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

# Cloudinary configuration
cloudinary.config(
    cloud_name="your_cloud_name",  # Thay bằng cloud name của bạn
    api_key="your_api_key",        # Thay bằng API key của bạn
    api_secret="your_api_secret"   # Thay bằng API secret của bạn
)

# Heyzine API configuration
HEYZINE_API_KEY = "your_heyzine_api_key"  # Thay bằng Heyzine API key
HEYZINE_API_URL = "https://heyzine.com/api/v1"

# Gemini AI configuration
genai.configure(api_key="AIzaSyAkECyB7OhyuhcrsjdHR9K7yd_XnqeXR9k")
gemini_model = genai.GenerativeModel("gemini-2.0-flash")

# Ensure upload folder exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def extract_text_from_pdf(pdf_file):
    """Extract text from PDF file"""
    try:
        pdf_reader = PyPDF2.PdfReader(pdf_file)
        pages_text = []
        
        for page_num, page in enumerate(pdf_reader.pages):
            text = page.extract_text()
            pages_text.append({
                'page_number': page_num + 1,
                'content': text.strip()
            })
        
        return pages_text
    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
        return []

def upload_to_cloudinary(file_path, public_id=None):
    """Upload file to Cloudinary"""
    try:
        result = cloudinary.uploader.upload(
            file_path,
            public_id=public_id,
            resource_type="auto",
            folder="ebooks"
        )
        return result
    except Exception as e:
        print(f"Error uploading to Cloudinary: {e}")
        return None

def create_heyzine_flipbook(pdf_url, title="Untitled Book"):
    """Create flipbook using Heyzine API"""
    try:
        headers = {
            'Authorization': f'Bearer {HEYZINE_API_KEY}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'title': title,
            'pdf_url': pdf_url,
            'settings': {
                'page_mode': 'single',  # Single page mode
                'auto_flip': False,
                'zoom_enabled': True,
                'download_enabled': False
            }
        }
        
        response = requests.post(
            f"{HEYZINE_API_URL}/flipbooks",
            headers=headers,
            json=data
        )
        
        if response.status_code == 201:
            return response.json()
        else:
            print(f"Heyzine API error: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"Error creating Heyzine flipbook: {e}")
        return None

def generate_teaching_script(page_content, page_number, book_title):
    """Generate teaching script for a page using Gemini AI"""
    try:
        prompt = f"""
        Bạn là giáo viên Hóa học chuyên nghiệp. Hãy tạo kịch bản giảng dạy cho trang {page_number} của sách "{book_title}".
        
        Nội dung trang:
        {page_content}
        
        Yêu cầu:
        1. Tạo kịch bản giảng dạy ngắn gọn (2-3 phút)
        2. Giải thích khái niệm một cách dễ hiểu
        3. Đưa ra ví dụ thực tế
        4. Tạo câu hỏi kiểm tra hiểu biết
        5. Sử dụng ngôn ngữ phù hợp với học sinh cấp 2
        
        Trả về JSON format:
        {{
            "script": "Kịch bản giảng dạy...",
            "key_concepts": ["khái niệm 1", "khái niệm 2"],
            "examples": ["ví dụ 1", "ví dụ 2"],
            "questions": ["câu hỏi 1", "câu hỏi 2"],
            "duration_minutes": 3
        }}
        """
        
        response = gemini_model.generate_content(prompt)
        
        # Parse JSON response
        script_data = json.loads(response.text.strip())
        return script_data
        
    except Exception as e:
        print(f"Error generating teaching script: {e}")
        return {
            "script": f"Nội dung trang {page_number}: {page_content[:200]}...",
            "key_concepts": [],
            "examples": [],
            "questions": [],
            "duration_minutes": 2
        }

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})

@app.route('/upload-pdf', methods=['POST'])
def upload_pdf():
    """Upload PDF and process it"""
    try:
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'Invalid file type. Only PDF allowed'}), 400
        
        # Get additional data
        title = request.form.get('title', 'Untitled Book')
        author = request.form.get('author', 'Unknown Author')
        subject = request.form.get('subject', 'Chemistry')
        
        # Generate unique ID
        book_id = str(uuid.uuid4())
        
        # Save file temporarily
        filename = secure_filename(file.filename)
        temp_path = os.path.join(UPLOAD_FOLDER, f"{book_id}_{filename}")
        file.save(temp_path)
        
        # Upload to Cloudinary
        print("📤 Uploading to Cloudinary...")
        cloudinary_result = upload_to_cloudinary(temp_path, f"ebook_{book_id}")
        if not cloudinary_result:
            return jsonify({'error': 'Failed to upload to Cloudinary'}), 500
        
        pdf_url = cloudinary_result['secure_url']
        
        # Create Heyzine flipbook
        print("📖 Creating Heyzine flipbook...")
        heyzine_result = create_heyzine_flipbook(pdf_url, title)
        if not heyzine_result:
            return jsonify({'error': 'Failed to create flipbook'}), 500
        
        flipbook_url = heyzine_result.get('embed_url', '')
        
        # Extract text from PDF
        print("📝 Extracting text from PDF...")
        with open(temp_path, 'rb') as pdf_file:
            pages_text = extract_text_from_pdf(pdf_file)
        
        # Generate teaching scripts for each page
        print("🤖 Generating AI teaching scripts...")
        pages_with_scripts = []
        
        for page_data in pages_text:
            if page_data['content'].strip():  # Only process pages with content
                script = generate_teaching_script(
                    page_data['content'], 
                    page_data['page_number'], 
                    title
                )
                
                pages_with_scripts.append({
                    'page_number': page_data['page_number'],
                    'content': page_data['content'],
                    'teaching_script': script
                })
        
        # Clean up temporary file
        os.remove(temp_path)
        
        # Prepare response
        result = {
            'book_id': book_id,
            'title': title,
            'author': author,
            'subject': subject,
            'pdf_url': pdf_url,
            'flipbook_url': flipbook_url,
            'heyzine_data': heyzine_result,
            'pages': pages_with_scripts,
            'total_pages': len(pages_with_scripts),
            'created_at': datetime.now().isoformat()
        }
        
        print(f"✅ Successfully processed book: {title}")
        return jsonify(result), 201
        
    except Exception as e:
        print(f"❌ Error processing PDF: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/books', methods=['GET'])
def get_books():
    """Get list of all books"""
    # This would typically fetch from database
    # For now, return empty list
    return jsonify({'books': []})

@app.route('/books/<book_id>', methods=['GET'])
def get_book(book_id):
    """Get specific book details"""
    # This would typically fetch from database
    return jsonify({'error': 'Book not found'}), 404

if __name__ == '__main__':
    print("🚀 Starting EBook Backend API Server...")
    print("📚 Cloudinary: Configured")
    print("📖 Heyzine API: Configured") 
    print("🤖 Gemini AI: Configured")
    print("🌐 Server: http://localhost:5001")
    app.run(host='0.0.0.0', port=5001, debug=True)
