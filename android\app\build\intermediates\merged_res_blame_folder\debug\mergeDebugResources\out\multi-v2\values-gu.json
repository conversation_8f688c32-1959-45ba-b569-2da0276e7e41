{"logs": [{"outputFile": "com.example.sachdientudemo.app-mergeDebugResources-46:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\62e42733fc48d7e4200114b93a9c82fb\\transformed\\core-1.13.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "29,30,31,32,33,34,35,59", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2788,2882,2985,3082,3184,3286,3384,6143", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "2877,2980,3077,3179,3281,3379,3501,6239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\906adeab1fc54800ccbfe1f925d79fc0\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-gu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4488", "endColumns": "146", "endOffsets": "4630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\61d3d4fe2dd837d40b39785170a0ad2d\\transformed\\jetified-play-services-base-18.1.0\\res\\values-gu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,459,580,688,822,940,1047,1143,1287,1391,1551,1672,1811,1957,2014,2076", "endColumns": "103,161,120,107,133,117,106,95,143,103,159,120,138,145,56,61,77", "endOffsets": "296,458,579,687,821,939,1046,1142,1286,1390,1550,1671,1810,1956,2013,2075,2153"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3506,3614,3780,3905,4017,4155,4277,4388,4635,4783,4891,5055,5180,5323,5473,5534,5600", "endColumns": "107,165,124,111,137,121,110,99,147,107,163,124,142,149,60,65,81", "endOffsets": "3609,3775,3900,4012,4150,4272,4383,4483,4778,4886,5050,5175,5318,5468,5529,5595,5677"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2ef2678f1ebb7c2f233ed01d9a85f9da\\transformed\\appcompat-1.1.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,886,977,1069,1164,1258,1359,1452,1547,1641,1732,1823,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,77,90,91,94,93,100,92,94,93,90,90,78,105,100,96,108,99,109,159,102,79", "endOffsets": "207,311,418,505,605,725,803,881,972,1064,1159,1253,1354,1447,1542,1636,1727,1818,1897,2003,2104,2201,2310,2410,2520,2680,2783,2863"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,886,977,1069,1164,1258,1359,1452,1547,1641,1732,1823,1902,2008,2109,2206,2315,2415,2525,2685,6063", "endColumns": "106,103,106,86,99,119,77,77,90,91,94,93,100,92,94,93,90,90,78,105,100,96,108,99,109,159,102,79", "endOffsets": "207,311,418,505,605,725,803,881,972,1064,1159,1253,1354,1447,1542,1636,1727,1818,1897,2003,2104,2201,2310,2410,2520,2680,2783,6138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\05840cd26434a51e94c2f67f7d2b7d17\\transformed\\preference-1.2.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,263,344,486,655,739", "endColumns": "71,85,80,141,168,83,81", "endOffsets": "172,258,339,481,650,734,816"}, "to": {"startLines": "54,55,56,57,60,61,62", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5682,5754,5840,5921,6244,6413,6497", "endColumns": "71,85,80,141,168,83,81", "endOffsets": "5749,5835,5916,6058,6408,6492,6574"}}]}]}