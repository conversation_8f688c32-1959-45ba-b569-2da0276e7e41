1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.sachdientudemo"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:41:13-72
25-->C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:42:13-50
27-->C:\Users\<USER>\AndroidStudioProjects\sachdientudemo\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29    </queries> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
30    <!-- <uses-sdk android:minSdkVersion="14" /> -->
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:10:5-79
31-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:10:22-76
32    <uses-permission android:name="android.permission.WAKE_LOCK" />
32-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:25:5-68
32-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:25:22-65
33    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
33-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:5-110
33-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:22-107
34    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
34-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:5-79
34-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:22-76
35    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
35-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:5-88
35-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:22-85
36    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
36-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:5-82
36-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:22-79
37
38    <permission
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
39        android:name="com.example.sachdientudemo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.example.sachdientudemo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
43
44    <application
45        android:name="android.app.Application"
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\62e42733fc48d7e4200114b93a9c82fb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
47        android:debuggable="true"
48        android:extractNativeLibs="true"
49        android:icon="@mipmap/ic_launcher"
50        android:label="sachdientudemo" >
51        <activity
52            android:name="com.example.sachdientudemo.MainActivity"
53            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
54            android:exported="true"
55            android:hardwareAccelerated="true"
56            android:launchMode="singleTop"
57            android:taskAffinity=""
58            android:theme="@style/LaunchTheme"
59            android:windowSoftInputMode="adjustResize" >
60
61            <!--
62                 Specifies an Android theme to apply to this Activity as soon as
63                 the Android process has started. This theme is visible to the user
64                 while the Flutter UI initializes. After that, this theme continues
65                 to determine the Window background behind the Flutter UI.
66            -->
67            <meta-data
68                android:name="io.flutter.embedding.android.NormalTheme"
69                android:resource="@style/NormalTheme" />
70
71            <intent-filter>
72                <action android:name="android.intent.action.MAIN" />
73
74                <category android:name="android.intent.category.LAUNCHER" />
75            </intent-filter>
76        </activity>
77        <!--
78             Don't delete the meta-data below.
79             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
80        -->
81        <meta-data
82            android:name="flutterEmbedding"
83            android:value="2" />
84
85        <service
85-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
86            android:name="com.google.firebase.components.ComponentDiscoveryService"
86-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
87            android:directBootAware="true"
87-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
88            android:exported="false" >
88-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:16:13-37
89            <meta-data
89-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
90                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
90-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
91                android:value="com.google.firebase.components.ComponentRegistrar" />
91-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
92            <meta-data
92-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
93                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
93-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
94                android:value="com.google.firebase.components.ComponentRegistrar" />
94-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
95            <meta-data
95-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:17:13-19:85
96                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
96-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:18:17-122
97                android:value="com.google.firebase.components.ComponentRegistrar" />
97-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:19:17-82
98            <meta-data
98-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:20:13-22:85
99                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
99-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:21:17-111
100                android:value="com.google.firebase.components.ComponentRegistrar" />
100-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6783651e2734e08af4858eabc6316ba2\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:22:17-82
101            <meta-data
101-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
102                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
102-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
104            <meta-data
104-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
105                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
105-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
106                android:value="com.google.firebase.components.ComponentRegistrar" />
106-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0803cbc4f2bfda3778cbd85c32a8778d\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
107            <meta-data
107-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:37:13-39:85
108                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
108-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:38:17-139
109                android:value="com.google.firebase.components.ComponentRegistrar" />
109-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:39:17-82
110            <meta-data
110-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:25:13-27:85
111                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
111-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:26:17-120
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:27:17-82
113            <meta-data
113-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:28:13-30:85
114                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
114-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:29:17-117
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdee737bc592a5c0226bae79adbef567\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:30:17-82
116            <meta-data
116-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
117                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
117-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
119            <meta-data
119-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
120                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
120-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab30b258f4944fce66644afa9c046f48\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
122            <meta-data
122-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2865c7ce03ea9d7b1bed052870d1372a\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
123                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
123-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2865c7ce03ea9d7b1bed052870d1372a\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\2865c7ce03ea9d7b1bed052870d1372a\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
125            <meta-data
125-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
126                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
126-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
128        </service>
129
130        <provider
130-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
131            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
131-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
132            android:authorities="com.example.sachdientudemo.flutter.image_provider"
132-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
133            android:exported="false"
133-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
134            android:grantUriPermissions="true" >
134-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
135            <meta-data
135-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
136                android:name="android.support.FILE_PROVIDER_PATHS"
136-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
137                android:resource="@xml/flutter_image_picker_file_paths" />
137-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
138        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
139        <service
139-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
140            android:name="com.google.android.gms.metadata.ModuleDependencies"
140-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
141            android:enabled="false"
141-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
142            android:exported="false" >
142-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
143            <intent-filter>
143-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
144                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
144-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
144-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
145            </intent-filter>
146
147            <meta-data
147-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
148                android:name="photopicker_activity:0:required"
148-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
149                android:value="" />
149-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+24\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
150        </service>
151
152        <uses-library
152-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
153            android:name="androidx.window.extensions"
153-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
154            android:required="false" />
154-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
155        <uses-library
155-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
156            android:name="androidx.window.sidecar"
156-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
157            android:required="false" />
157-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
158
159        <receiver
159-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:29:9-33:20
160            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
160-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:30:13-85
161            android:enabled="true"
161-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:31:13-35
162            android:exported="false" >
162-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:32:13-37
163        </receiver>
164
165        <service
165-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:35:9-38:40
166            android:name="com.google.android.gms.measurement.AppMeasurementService"
166-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:36:13-84
167            android:enabled="true"
167-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:37:13-35
168            android:exported="false" />
168-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:38:13-37
169        <service
169-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:39:9-43:72
170            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
170-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:40:13-87
171            android:enabled="true"
171-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:41:13-35
172            android:exported="false"
172-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:42:13-37
173            android:permission="android.permission.BIND_JOB_SERVICE" />
173-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b917eff2135ea31f63481a506bff523\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:43:13-69
174
175        <property
175-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:30:9-32:61
176            android:name="android.adservices.AD_SERVICES_CONFIG"
176-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:31:13-65
177            android:resource="@xml/ga_ad_services_config" />
177-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\accb74e5f2df6bdd3bbee34ce99c7340\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:32:13-58
178
179        <provider
179-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
180            android:name="com.google.firebase.provider.FirebaseInitProvider"
180-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
181            android:authorities="com.example.sachdientudemo.firebaseinitprovider"
181-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
182            android:directBootAware="true"
182-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
183            android:exported="false"
183-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
184            android:initOrder="100" />
184-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\da0bb0ccc32ae1c671a25dafde8772ac\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
185
186        <activity
186-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
187            android:name="com.google.android.gms.common.api.GoogleApiActivity"
187-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
188            android:exported="false"
188-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
189            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
189-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\61d3d4fe2dd837d40b39785170a0ad2d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
190
191        <provider
191-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
192            android:name="androidx.startup.InitializationProvider"
192-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
193            android:authorities="com.example.sachdientudemo.androidx-startup"
193-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
194            android:exported="false" >
194-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
195            <meta-data
195-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
196                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
196-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
197                android:value="androidx.startup" />
197-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
198            <meta-data
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
199                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
199-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
200                android:value="androidx.startup" />
200-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
201        </provider>
202
203        <uses-library
203-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\94305a301053fa4cadcf5a087b7a53c5\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
204            android:name="android.ext.adservices"
204-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\94305a301053fa4cadcf5a087b7a53c5\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
205            android:required="false" />
205-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\94305a301053fa4cadcf5a087b7a53c5\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
206
207        <meta-data
207-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\906adeab1fc54800ccbfe1f925d79fc0\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
208            android:name="com.google.android.gms.version"
208-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\906adeab1fc54800ccbfe1f925d79fc0\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
209            android:value="@integer/google_play_services_version" />
209-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\906adeab1fc54800ccbfe1f925d79fc0\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
210
211        <receiver
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
212            android:name="androidx.profileinstaller.ProfileInstallReceiver"
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
213            android:directBootAware="false"
213-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
214            android:enabled="true"
214-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
215            android:exported="true"
215-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
216            android:permission="android.permission.DUMP" >
216-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
217            <intent-filter>
217-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
218                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
218-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
218-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
219            </intent-filter>
220            <intent-filter>
220-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
221                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
221-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
221-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
222            </intent-filter>
223            <intent-filter>
223-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
224                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
224-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
224-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
225            </intent-filter>
226            <intent-filter>
226-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
227                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
227-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
227-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\027db3816755bfa5c52fa301b1cf847a\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
228            </intent-filter>
229        </receiver>
230    </application>
231
232</manifest>
