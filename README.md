# 📚 EBook Flutter App với AI Assistant

Ứng dụng đọc sách điện tử tích hợp AI Assistant sử dụng Flutter và Google Gemini AI.

## 🚀 Cách Chạy Ứng Dụng

### Phương pháp 1: Sử dụng script tự động (Khuyến nghị)

**Windows:**
```bash
start_app.bat
```

**Linux/macOS:**
```bash
chmod +x start_app.sh
./start_app.sh
```

### Phương pháp 2: Chạy thủ công

1. **Cài đặt dependencies Python:**
```bash
pip install flask flask-cors google-generativeai edge-tts pygame SpeechRecognition
```

2. **Khởi động AI Server:**
```bash
python assistant.py
```

3. **Chạy Flutter App (terminal mới):**
```bash
flutter run
```

## 🏗️ Kiến Trúc Hệ Thống

```
[Flutter App] ←→ [AI Server (assistant.py)] ←→ [Google Gemini API]
     ↓                    ↓                         ↓
[Heyzine Flipbook]   [Edge TTS]              [Text Generation]
     ↓                    ↓                         ↓
[PDF Display]        [Audio Output]          [Q&A, Summarization]
```

## 🔧 Tính Năng

- ✅ Đọc sách PDF qua Heyzine Flipbook
- ✅ AI Assistant với Google Gemini
- ✅ Text-to-Speech với Edge TTS
- ✅ Giao diện responsive (Mobile/Desktop)
- ✅ Tách riêng iframe và chat box
- ✅ Tự động khởi động AI Server

## 📱 Giao Diện

### Desktop Layout
```
┌─────────────────┬─────────────┐
│                 │             │
│   Flipbook      │ AI Assistant│
│   (70%)         │   (30%)     │
│                 │             │
└─────────────────┴─────────────┘
```

### Mobile Layout
```
┌─────────────────┐
│   Flipbook      │
│   (70%)         │
├─────────────────┤
│ AI Assistant    │
│   (30%)         │
└─────────────────┘
```

## 🛠️ Cấu Hình

1. **Google Gemini API Key:** Cập nhật trong `assistant.py`
2. **Firebase:** Cấu hình trong `firebase_options.dart`
3. **Heyzine URLs:** Lưu trong Firestore

## 📋 Yêu Cầu Hệ Thống

- Flutter SDK ≥ 3.8.1
- Python ≥ 3.8
- Node.js (cho web)
- Firebase project
- Google Gemini API key

## 🔍 Troubleshooting

**AI Server không khởi động:**
- Kiểm tra Python đã cài đặt
- Kiểm tra port 5000 có bị chiếm
- Chạy thủ công: `python assistant.py`

**Flutter build lỗi:**
- Chạy: `flutter clean && flutter pub get`
- Kiểm tra Firebase configuration

## 📞 Hỗ Trợ

Nếu gặp vấn đề, vui lòng kiểm tra:
1. Console output của AI Server
2. Flutter debug console
3. Browser developer tools (cho web)
